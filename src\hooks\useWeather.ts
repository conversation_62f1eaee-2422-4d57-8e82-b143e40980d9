import { useState, useEffect, useCallback } from 'react';
import { CurrentWeather, WeatherForecast, LoadingState } from '@/types';
import { weatherApi, getGeolocation } from '@/lib/api';
import { CACHE_DURATION, STORAGE_KEYS } from '@/lib/constants';
import { getFromLocalStorage, setToLocalStorage, isCacheValid, createCacheKey } from '@/lib/utils';

interface CachedWeatherData {
  data: CurrentWeather | WeatherForecast;
  timestamp: number;
  cacheKey: string;
}

interface UseWeatherOptions {
  city?: string;
  autoFetch?: boolean;
  useGeolocation?: boolean;
}

export function useWeather(options: UseWeatherOptions = {}) {
  const { city, autoFetch = true, useGeolocation = false } = options;

  const [weather, setWeather] = useState<CurrentWeather | null>(null);
  const [loading, setLoading] = useState<LoadingState>({
    isLoading: false,
    error: null
  });
  const [currentCity, setCurrentCity] = useState<string>(city || '');

  const fetchWeatherByCity = useCallback(async (cityName: string) => {
    if (!cityName.trim()) return;

    setLoading({ isLoading: true, error: null });
    const cacheKey = createCacheKey('weather', 'city', cityName);

    try {
      // Check cache first
      const cachedData = getFromLocalStorage<CachedWeatherData | null>(
        `${STORAGE_KEYS.CACHED_WEATHER}_${cacheKey}`,
        null
      );

      if (cachedData && isCacheValid(cachedData.timestamp, CACHE_DURATION.WEATHER)) {
        setWeather(cachedData.data as CurrentWeather);
        setLoading({ isLoading: false, error: null });
        return;
      }

      // Fetch fresh data
      const data = await weatherApi.getCurrentWeather(cityName);
      
      // Cache the data
      const cacheData: CachedWeatherData = {
        data,
        timestamp: Date.now(),
        cacheKey
      };
      setToLocalStorage(`${STORAGE_KEYS.CACHED_WEATHER}_${cacheKey}`, cacheData);

      setWeather(data);
      setCurrentCity(cityName);
      setLoading({ isLoading: false, error: null });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch weather';
      setLoading({ isLoading: false, error: errorMessage });
    }
  }, []);

  const fetchWeatherByLocation = useCallback(async () => {
    setLoading({ isLoading: true, error: null });

    try {
      const position = await getGeolocation();
      const { latitude, longitude } = position.coords;
      
      const cacheKey = createCacheKey('weather', 'coords', latitude.toString(), longitude.toString());

      // Check cache first
      const cachedData = getFromLocalStorage<CachedWeatherData | null>(
        `${STORAGE_KEYS.CACHED_WEATHER}_${cacheKey}`,
        null
      );

      if (cachedData && isCacheValid(cachedData.timestamp, CACHE_DURATION.WEATHER)) {
        setWeather(cachedData.data as CurrentWeather);
        setLoading({ isLoading: false, error: null });
        return;
      }

      // Fetch fresh data
      const data = await weatherApi.getWeatherByCoords(latitude, longitude);
      
      // Cache the data
      const cacheData: CachedWeatherData = {
        data,
        timestamp: Date.now(),
        cacheKey
      };
      setToLocalStorage(`${STORAGE_KEYS.CACHED_WEATHER}_${cacheKey}`, cacheData);

      setWeather(data);
      setCurrentCity(data.name);
      setLoading({ isLoading: false, error: null });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get location weather';
      setLoading({ isLoading: false, error: errorMessage });
    }
  }, []);

  const refreshWeather = useCallback(() => {
    // Clear cache and fetch fresh data
    const cachedKeys = Object.keys(localStorage).filter(key => 
      key.startsWith(STORAGE_KEYS.CACHED_WEATHER)
    );
    cachedKeys.forEach(key => localStorage.removeItem(key));
    
    if (useGeolocation) {
      fetchWeatherByLocation();
    } else if (currentCity) {
      fetchWeatherByCity(currentCity);
    }
  }, [useGeolocation, currentCity, fetchWeatherByLocation, fetchWeatherByCity]);

  useEffect(() => {
    if (autoFetch) {
      if (useGeolocation) {
        fetchWeatherByLocation();
      } else if (city) {
        fetchWeatherByCity(city);
      }
    }
  }, [autoFetch, useGeolocation, city, fetchWeatherByLocation, fetchWeatherByCity]);

  return {
    weather,
    loading: loading.isLoading,
    error: loading.error,
    currentCity,
    fetchWeatherByCity,
    fetchWeatherByLocation,
    refreshWeather,
  };
}

export function useWeatherForecast(city?: string) {
  const [forecast, setForecast] = useState<WeatherForecast | null>(null);
  const [loading, setLoading] = useState<LoadingState>({
    isLoading: false,
    error: null
  });

  const fetchForecast = useCallback(async (cityName: string) => {
    if (!cityName.trim()) return;

    setLoading({ isLoading: true, error: null });
    const cacheKey = createCacheKey('forecast', cityName);

    try {
      // Check cache first
      const cachedData = getFromLocalStorage<CachedWeatherData | null>(
        `${STORAGE_KEYS.CACHED_WEATHER}_${cacheKey}`,
        null
      );

      if (cachedData && isCacheValid(cachedData.timestamp, CACHE_DURATION.WEATHER)) {
        setForecast(cachedData.data as WeatherForecast);
        setLoading({ isLoading: false, error: null });
        return;
      }

      // Fetch fresh data
      const data = await weatherApi.getForecast(cityName);
      
      // Cache the data
      const cacheData: CachedWeatherData = {
        data,
        timestamp: Date.now(),
        cacheKey
      };
      setToLocalStorage(`${STORAGE_KEYS.CACHED_WEATHER}_${cacheKey}`, cacheData);

      setForecast(data);
      setLoading({ isLoading: false, error: null });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch forecast';
      setLoading({ isLoading: false, error: errorMessage });
    }
  }, []);

  useEffect(() => {
    if (city) {
      fetchForecast(city);
    }
  }, [city, fetchForecast]);

  return {
    forecast,
    loading: loading.isLoading,
    error: loading.error,
    fetchForecast,
  };
}

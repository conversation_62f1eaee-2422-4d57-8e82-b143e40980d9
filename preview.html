<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>News & Weather Dashboard - Preview</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        border: "hsl(214.3 31.8% 91.4%)",
                        background: "hsl(0 0% 100%)",
                        foreground: "hsl(222.2 84% 4.9%)",
                        primary: "hsl(221.2 83.2% 53.3%)",
                        secondary: "hsl(210 40% 96%)",
                        muted: "hsl(210 40% 96%)",
                        accent: "hsl(210 40% 96%)",
                        card: "hsl(0 0% 100%)",
                    }
                }
            }
        }
    </script>
    <style>
        .dark {
            --tw-bg-opacity: 1;
            background-color: hsl(222.2 84% 4.9% / var(--tw-bg-opacity));
            color: hsl(210 40% 98%);
        }
    </style>
</head>
<body class="bg-background text-foreground">
    <div class="min-h-screen">
        <!-- Header -->
        <header class="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur">
            <div class="container mx-auto flex h-16 items-center px-4">
                <div class="flex items-center space-x-2">
                    <div class="h-8 w-8 rounded-lg bg-blue-600 flex items-center justify-center">
                        <span class="text-white font-bold text-sm">ND</span>
                    </div>
                    <span class="font-bold">News Dashboard</span>
                </div>
                <nav class="hidden md:flex items-center space-x-6 text-sm font-medium ml-6">
                    <a href="#" class="text-foreground">Dashboard</a>
                    <a href="#" class="text-foreground/60 hover:text-foreground">News</a>
                    <a href="#" class="text-foreground/60 hover:text-foreground">Weather</a>
                    <a href="#" class="text-foreground/60 hover:text-foreground">Countries</a>
                    <a href="#" class="text-foreground/60 hover:text-foreground">Crypto</a>
                </nav>
                <div class="ml-auto flex items-center space-x-4">
                    <input type="search" placeholder="Search news..." class="px-3 py-2 border rounded-md w-64">
                    <button onclick="toggleTheme()" class="p-2 rounded-md hover:bg-gray-100">🌙</button>
                </div>
            </div>
        </header>

        <main class="container mx-auto p-6">
            <!-- Dashboard Header -->
            <div class="space-y-2 mb-6">
                <h1 class="text-3xl font-bold tracking-tight">Dashboard</h1>
                <p class="text-gray-600">Stay updated with the latest news, weather, and market information.</p>
            </div>

            <!-- Widgets Grid -->
            <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                <!-- Weather Widget -->
                <div class="bg-white rounded-lg border shadow-sm">
                    <div class="p-6 pb-4">
                        <h3 class="text-lg font-semibold flex items-center">
                            <span class="mr-2">🌤️</span>
                            Weather
                        </h3>
                    </div>
                    <div class="p-6 pt-0 space-y-4">
                        <div class="flex space-x-2">
                            <input type="text" placeholder="Search city..." class="flex-1 px-3 py-2 border rounded-md text-sm">
                            <button class="px-3 py-2 border rounded-md">🔍</button>
                            <button class="px-3 py-2 border rounded-md">📍</button>
                        </div>
                        <div class="space-y-4">
                            <div class="flex items-center space-x-2 text-sm text-gray-600">
                                <span>📍</span>
                                <span>New York, US</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-4">
                                    <span class="text-4xl">☀️</span>
                                    <div>
                                        <div class="text-3xl font-bold">22°C</div>
                                        <div class="text-sm text-gray-600">Clear sky</div>
                                    </div>
                                </div>
                                <div class="text-right text-sm">
                                    <div>Feels like</div>
                                    <div class="font-semibold">25°C</div>
                                </div>
                            </div>
                            <div class="grid grid-cols-2 gap-4 text-sm">
                                <div class="flex items-center space-x-2">
                                    <span class="text-blue-500">💧</span>
                                    <span>Humidity: 65%</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="text-gray-500">💨</span>
                                    <span>Wind: 3.2 m/s</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- News Widget -->
                <div class="md:col-span-1 lg:col-span-2 bg-white rounded-lg border shadow-sm">
                    <div class="p-6 pb-4">
                        <h3 class="text-lg font-semibold flex items-center justify-between">
                            <span class="flex items-center">
                                <span class="mr-2">📰</span>
                                Latest News
                            </span>
                            <button class="text-sm text-blue-600 hover:underline">View All</button>
                        </h3>
                    </div>
                    <div class="p-6 pt-0 space-y-4">
                        <div class="flex space-x-2 mb-4">
                            <input type="text" placeholder="Search news..." class="flex-1 px-3 py-2 border rounded-md text-sm">
                            <button class="px-3 py-2 border rounded-md">🔍</button>
                        </div>
                        <div class="space-y-4">
                            <div class="flex space-x-4 p-3 rounded-lg border hover:bg-gray-50">
                                <div class="w-24 h-16 bg-gray-200 rounded flex items-center justify-center text-xs">
                                    📷 Image
                                </div>
                                <div class="flex-1">
                                    <h4 class="font-semibold text-sm mb-1">Breaking: Major Tech Announcement</h4>
                                    <p class="text-xs text-gray-600 mb-2">Latest developments in technology sector...</p>
                                    <div class="flex items-center justify-between text-xs text-gray-500">
                                        <span>TechNews • 2 hours ago</span>
                                        <div class="flex space-x-1">
                                            <button>❤️</button>
                                            <button>🔗</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="flex space-x-4 p-3 rounded-lg border hover:bg-gray-50">
                                <div class="w-24 h-16 bg-gray-200 rounded flex items-center justify-center text-xs">
                                    📷 Image
                                </div>
                                <div class="flex-1">
                                    <h4 class="font-semibold text-sm mb-1">Global Market Update</h4>
                                    <p class="text-xs text-gray-600 mb-2">Financial markets show positive trends...</p>
                                    <div class="flex items-center justify-between text-xs text-gray-500">
                                        <span>Business Today • 4 hours ago</span>
                                        <div class="flex space-x-1">
                                            <button>❤️</button>
                                            <button>🔗</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Crypto Widget -->
                <div class="bg-white rounded-lg border shadow-sm">
                    <div class="p-6 pb-4">
                        <h3 class="text-lg font-semibold flex items-center">
                            <span class="mr-2">📈</span>
                            Cryptocurrency
                        </h3>
                    </div>
                    <div class="p-6 pt-0 space-y-4">
                        <div class="flex space-x-2">
                            <input type="text" placeholder="Search crypto..." class="flex-1 px-3 py-2 border rounded-md text-sm">
                            <select class="px-3 py-2 border rounded-md text-sm">
                                <option>USD</option>
                                <option>EUR</option>
                            </select>
                        </div>
                        <div class="space-y-3">
                            <div class="flex items-center justify-between p-3 rounded-lg border">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center text-white text-xs">₿</div>
                                    <div>
                                        <div class="font-semibold text-sm">Bitcoin</div>
                                        <div class="text-xs text-gray-600">BTC</div>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="font-semibold text-sm">$43,250</div>
                                    <div class="text-xs text-green-600">+2.5%</div>
                                </div>
                            </div>
                            <div class="flex items-center justify-between p-3 rounded-lg border">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs">Ξ</div>
                                    <div>
                                        <div class="font-semibold text-sm">Ethereum</div>
                                        <div class="text-xs text-gray-600">ETH</div>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="font-semibold text-sm">$2,650</div>
                                    <div class="text-xs text-red-600">-1.2%</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Country Widget -->
                <div class="md:col-span-1 lg:col-span-2 bg-white rounded-lg border shadow-sm">
                    <div class="p-6 pb-4">
                        <h3 class="text-lg font-semibold flex items-center">
                            <span class="mr-2">🌍</span>
                            Country Information
                        </h3>
                    </div>
                    <div class="p-6 pt-0 space-y-4">
                        <div class="flex space-x-2">
                            <input type="text" placeholder="Search countries..." class="flex-1 px-3 py-2 border rounded-md text-sm">
                            <select class="px-3 py-2 border rounded-md text-sm w-48">
                                <option>Select a country</option>
                                <option>🇺🇸 United States</option>
                                <option>🇬🇧 United Kingdom</option>
                                <option>🇫🇷 France</option>
                            </select>
                        </div>
                        <div class="flex items-start space-x-4 p-4 border rounded-lg">
                            <div class="w-24 h-16 bg-gray-200 rounded flex items-center justify-center">
                                🇺🇸
                            </div>
                            <div class="flex-1">
                                <h3 class="text-xl font-bold">United States</h3>
                                <p class="text-sm text-gray-600">United States of America</p>
                                <div class="flex items-center space-x-4 mt-2 text-sm">
                                    <span>📍 North America</span>
                                    <span class="text-gray-600">• Northern America</span>
                                </div>
                            </div>
                        </div>
                        <div class="grid grid-cols-2 gap-4">
                            <div class="p-3 border rounded-lg">
                                <div class="flex items-center space-x-2 mb-1">
                                    <span class="text-blue-500">👥</span>
                                    <span class="text-sm font-medium">Population</span>
                                </div>
                                <p class="text-lg font-bold">331,002,651</p>
                            </div>
                            <div class="p-3 border rounded-lg">
                                <div class="flex items-center space-x-2 mb-1">
                                    <span class="text-green-500">🗺️</span>
                                    <span class="text-sm font-medium">Area</span>
                                </div>
                                <p class="text-lg font-bold">9,833,517 km²</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        function toggleTheme() {
            document.documentElement.classList.toggle('dark');
            const button = event.target;
            button.textContent = document.documentElement.classList.contains('dark') ? '☀️' : '🌙';
        }

        // Add some interactivity
        document.addEventListener('DOMContentLoaded', function() {
            // Simulate loading real data
            console.log('News & Weather Dashboard Preview Loaded');
            console.log('API Keys configured:');
            console.log('- News API: 2e4cecfcab02441f93a75ba5662b5966');
            console.log('- Weather API: 39befe1e4e1e409180a123709251408');
            
            // Add click handlers for demo
            document.querySelectorAll('button').forEach(button => {
                if (!button.onclick) {
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        console.log('Button clicked:', button.textContent);
                    });
                }
            });
        });
    </script>
</body>
</html>

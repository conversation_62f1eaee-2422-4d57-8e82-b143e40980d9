{"framework": "nextjs", "buildCommand": "npm run build", "devCommand": "npm run dev", "installCommand": "npm install", "outputDirectory": ".next", "functions": {"app/**/*.{js,ts,tsx}": {"maxDuration": 30}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}], "rewrites": [{"source": "/api/:path*", "destination": "/api/:path*"}]}
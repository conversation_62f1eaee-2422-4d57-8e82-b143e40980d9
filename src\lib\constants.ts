// API Base URLs
export const API_ENDPOINTS = {
  NEWS_API: 'https://newsapi.org/v2',
  GNEWS_API: 'https://gnews.io/api/v4',
  WEATHER_API: 'https://api.openweathermap.org/data/2.5',
  WEATHER_API_ALT: 'https://api.weatherapi.com/v1',
  COUNTRIES_API: 'https://restcountries.com/v3.1',
  COINGECKO_API: 'https://api.coingecko.com/api/v3',
  UNSPLASH_API: 'https://api.unsplash.com',
  JSONPLACEHOLDER: 'https://jsonplaceholder.typicode.com',
} as const;

// API Keys
export const API_KEYS = {
  NEWS_API: process.env.NEXT_PUBLIC_NEWS_API_KEY,
  GNEWS_API: process.env.NEXT_PUBLIC_GNEWS_API_KEY,
  WEATHER_API: process.env.NEXT_PUBLIC_WEATHER_API_KEY,
  WEATHER_API_ALT: process.env.NEXT_PUBLIC_WEATHERAPI_KEY,
  UNSPLASH_API: process.env.NEXT_PUBLIC_UNSPLASH_API_KEY,
  COINGECKO_API: process.env.NEXT_PUBLIC_COINGECKO_API_KEY,
} as const;

// News Categories
export const NEWS_CATEGORIES = [
  { value: 'general', label: 'General' },
  { value: 'business', label: 'Business' },
  { value: 'entertainment', label: 'Entertainment' },
  { value: 'health', label: 'Health' },
  { value: 'science', label: 'Science' },
  { value: 'sports', label: 'Sports' },
  { value: 'technology', label: 'Technology' },
] as const;

// Countries for news filtering
export const NEWS_COUNTRIES = [
  { value: 'us', label: 'United States' },
  { value: 'gb', label: 'United Kingdom' },
  { value: 'ca', label: 'Canada' },
  { value: 'au', label: 'Australia' },
  { value: 'de', label: 'Germany' },
  { value: 'fr', label: 'France' },
  { value: 'in', label: 'India' },
  { value: 'jp', label: 'Japan' },
] as const;

// Weather units
export const WEATHER_UNITS = {
  METRIC: 'metric',
  IMPERIAL: 'imperial',
  KELVIN: 'standard',
} as const;

// Default cities for weather
export const DEFAULT_CITIES = [
  'New York',
  'London',
  'Tokyo',
  'Paris',
  'Sydney',
  'Mumbai',
  'Berlin',
  'Toronto',
] as const;

// Cache durations (in milliseconds)
export const CACHE_DURATION = {
  NEWS: 5 * 60 * 1000, // 5 minutes
  WEATHER: 10 * 60 * 1000, // 10 minutes
  COUNTRIES: 24 * 60 * 60 * 1000, // 24 hours
  CRYPTO: 2 * 60 * 1000, // 2 minutes
} as const;

// Rate limits
export const RATE_LIMITS = {
  NEWS_API: 100, // requests per day
  GNEWS_API: 100, // requests per day
  WEATHER_API: 1000, // requests per day
  COINGECKO_API: 50, // requests per minute
} as const;

// Error messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network error. Please check your connection.',
  API_KEY_MISSING: 'API key is missing. Please check your configuration.',
  RATE_LIMIT_EXCEEDED: 'Rate limit exceeded. Please try again later.',
  INVALID_RESPONSE: 'Invalid response from server.',
  GEOLOCATION_DENIED: 'Geolocation access denied.',
  GEOLOCATION_UNAVAILABLE: 'Geolocation is not available.',
  GENERIC_ERROR: 'Something went wrong. Please try again.',
} as const;

// Local storage keys
export const STORAGE_KEYS = {
  USER_PREFERENCES: 'user_preferences',
  CACHED_NEWS: 'cached_news',
  CACHED_WEATHER: 'cached_weather',
  CACHED_COUNTRIES: 'cached_countries',
  CACHED_CRYPTO: 'cached_crypto',
  FAVORITE_ARTICLES: 'favorite_articles',
  RECENT_SEARCHES: 'recent_searches',
} as const;

// Theme options
export const THEME_OPTIONS = [
  { value: 'light', label: 'Light' },
  { value: 'dark', label: 'Dark' },
  { value: 'system', label: 'System' },
] as const;

// Currency options
export const CURRENCY_OPTIONS = [
  { value: 'usd', label: 'USD ($)' },
  { value: 'eur', label: 'EUR (€)' },
  { value: 'gbp', label: 'GBP (£)' },
  { value: 'jpy', label: 'JPY (¥)' },
  { value: 'cad', label: 'CAD (C$)' },
  { value: 'aud', label: 'AUD (A$)' },
] as const;

// Temperature units
export const TEMPERATURE_UNITS = [
  { value: 'celsius', label: 'Celsius (°C)' },
  { value: 'fahrenheit', label: 'Fahrenheit (°F)' },
] as const;

'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Skeleton } from '@/components/ui/skeleton';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  TrendingUp, 
  TrendingDown, 
  Search, 
  RefreshCw, 
  DollarSign,
  BarChart3,
  ArrowUpRight,
  ArrowDownRight
} from 'lucide-react';
import { useCrypto } from '@/hooks/useCrypto';
import { useUserPreferences } from '@/contexts/UserPreferencesContext';
import { formatCurrency, formatPercentage, formatLargeNumber } from '@/lib/utils';
import { CURRENCY_OPTIONS } from '@/lib/constants';
import { useToast } from '@/hooks/useToast';

export function CryptoWidget() {
  const { preferences } = useUserPreferences();
  const { toast } = useToast();
  
  const [selectedCurrency, setSelectedCurrency] = useState(preferences.currency);
  const [searchQuery, setSearchQuery] = useState('');

  const {
    cryptocurrencies,
    loading,
    error,
    fetchCryptocurrencies,
    refreshCrypto,
  } = useCrypto({
    currency: selectedCurrency,
    perPage: 10,
    autoFetch: true,
  });

  const handleCurrencyChange = (currency: string) => {
    setSelectedCurrency(currency);
    toast({
      title: "Currency Changed",
      description: `Showing prices in ${currency.toUpperCase()}`,
    });
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // For now, just filter the existing results
    // In a real app, you might want to call a search API
  };

  const filteredCryptos = cryptocurrencies.filter(crypto =>
    crypto.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    crypto.symbol.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TrendingUp className="h-5 w-5" />
            <span>Cryptocurrency</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {Array.from({ length: 5 }).map((_, i) => (
            <div key={i} className="flex items-center space-x-3">
              <Skeleton className="h-8 w-8 rounded-full" />
              <div className="flex-1 space-y-1">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-3 w-16" />
              </div>
              <div className="text-right space-y-1">
                <Skeleton className="h-4 w-16" />
                <Skeleton className="h-3 w-12" />
              </div>
            </div>
          ))}
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TrendingUp className="h-5 w-5" />
            <span>Cryptocurrency</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-muted-foreground mb-4">{error}</p>
            <Button onClick={refreshCrypto} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <TrendingUp className="h-5 w-5" />
            <span>Cryptocurrency</span>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={refreshCrypto}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Search and Currency Selector */}
        <div className="space-y-4">
          <form onSubmit={handleSearch} className="flex space-x-2">
            <Input
              placeholder="Search crypto..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="flex-1"
            />
            <Button type="submit" size="icon" variant="outline">
              <Search className="h-4 w-4" />
            </Button>
          </form>

          <Select value={selectedCurrency} onValueChange={handleCurrencyChange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {CURRENCY_OPTIONS.map((currency) => (
                <SelectItem key={currency.value} value={currency.value}>
                  {currency.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Cryptocurrency List */}
        {filteredCryptos.length > 0 ? (
          <div className="space-y-3">
            {filteredCryptos.slice(0, 8).map((crypto) => {
              const isPositive = crypto.price_change_percentage_24h >= 0;
              const PriceIcon = isPositive ? ArrowUpRight : ArrowDownRight;
              
              return (
                <div
                  key={crypto.id}
                  className="flex items-center justify-between p-3 rounded-lg border hover:bg-accent/50 transition-colors"
                >
                  <div className="flex items-center space-x-3">
                    <div className="relative h-8 w-8">
                      <Image
                        src={crypto.image}
                        alt={crypto.name}
                        fill
                        className="rounded-full"
                        sizes="32px"
                      />
                    </div>
                    <div>
                      <div className="font-semibold text-sm">{crypto.name}</div>
                      <div className="text-xs text-muted-foreground uppercase">
                        {crypto.symbol}
                      </div>
                    </div>
                  </div>

                  <div className="text-right">
                    <div className="font-semibold text-sm">
                      {formatCurrency(crypto.current_price, selectedCurrency)}
                    </div>
                    <div className={`flex items-center space-x-1 text-xs ${
                      isPositive ? 'text-green-600' : 'text-red-600'
                    }`}>
                      <PriceIcon className="h-3 w-3" />
                      <span>
                        {formatPercentage(crypto.price_change_percentage_24h)}
                      </span>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          <div className="text-center py-8">
            <p className="text-muted-foreground">No cryptocurrencies found</p>
          </div>
        )}

        {/* Market Summary */}
        {cryptocurrencies.length > 0 && (
          <div className="border-t pt-4">
            <h4 className="font-semibold text-sm mb-3">Market Summary</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="space-y-1">
                <div className="flex items-center space-x-1 text-muted-foreground">
                  <BarChart3 className="h-3 w-3" />
                  <span>Total Market Cap</span>
                </div>
                <div className="font-semibold">
                  {formatCurrency(
                    cryptocurrencies.reduce((sum, crypto) => sum + crypto.market_cap, 0),
                    selectedCurrency
                  )}
                </div>
              </div>
              <div className="space-y-1">
                <div className="flex items-center space-x-1 text-muted-foreground">
                  <DollarSign className="h-3 w-3" />
                  <span>24h Volume</span>
                </div>
                <div className="font-semibold">
                  {formatCurrency(
                    cryptocurrencies.reduce((sum, crypto) => sum + crypto.total_volume, 0),
                    selectedCurrency
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* View More */}
        {cryptocurrencies.length > 8 && (
          <div className="text-center">
            <Button variant="outline" asChild>
              <a href="/crypto">View All Cryptocurrencies</a>
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

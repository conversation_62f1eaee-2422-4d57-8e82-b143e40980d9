/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    domains: [
      'newsapi.org',
      'images.unsplash.com',
      'openweathermap.org',
      'flagcdn.com',
      'restcountries.com',
      'assets.coingecko.com'
    ],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
      },
    ],
  },
  experimental: {
    appDir: true,
  },
}

module.exports = nextConfig

import { useState, useEffect, useCallback } from 'react';
import { NewsResponse, NewsCategory, LoadingState } from '@/types';
import { newsApi } from '@/lib/api';
import { CACHE_DURATION, STORAGE_KEYS } from '@/lib/constants';
import { getFromLocalStorage, setToLocalStorage, isCacheValid, createCacheKey } from '@/lib/utils';

interface CachedNewsData {
  data: NewsResponse;
  timestamp: number;
  cacheKey: string;
}

interface UseNewsOptions {
  category?: NewsCategory;
  country?: string;
  pageSize?: number;
  page?: number;
  autoFetch?: boolean;
}

export function useNews(options: UseNewsOptions = {}) {
  const {
    category = 'general',
    country = 'us',
    pageSize = 20,
    page = 1,
    autoFetch = true
  } = options;

  const [news, setNews] = useState<NewsResponse | null>(null);
  const [loading, setLoading] = useState<LoadingState>({
    isLoading: false,
    error: null
  });

  const cacheKey = createCacheKey('news', category, country, pageSize.toString(), page.toString());

  const fetchNews = useCallback(async () => {
    setLoading({ isLoading: true, error: null });

    try {
      // Check cache first
      const cachedData = getFromLocalStorage<CachedNewsData | null>(
        `${STORAGE_KEYS.CACHED_NEWS}_${cacheKey}`,
        null
      );

      if (cachedData && isCacheValid(cachedData.timestamp, CACHE_DURATION.NEWS)) {
        setNews(cachedData.data);
        setLoading({ isLoading: false, error: null });
        return;
      }

      // Fetch fresh data
      const data = await newsApi.getTopHeadlines(category, country, pageSize, page);

      // Cache the data
      const cacheData: CachedNewsData = {
        data,
        timestamp: Date.now(),
        cacheKey
      };
      setToLocalStorage(`${STORAGE_KEYS.CACHED_NEWS}_${cacheKey}`, cacheData);

      setNews(data);
      setLoading({ isLoading: false, error: null });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch news';
      setLoading({ isLoading: false, error: errorMessage });
    }
  }, [category, country, pageSize, page, cacheKey]);

  const searchNews = useCallback(async (
    query: string,
    sortBy: 'relevancy' | 'popularity' | 'publishedAt' = 'publishedAt'
  ) => {
    setLoading({ isLoading: true, error: null });

    try {
      const searchCacheKey = createCacheKey('search', query, sortBy, pageSize.toString(), page.toString());
      
      // Check cache first
      const cachedData = getFromLocalStorage<CachedNewsData | null>(
        `${STORAGE_KEYS.CACHED_NEWS}_${searchCacheKey}`,
        null
      );

      if (cachedData && isCacheValid(cachedData.timestamp, CACHE_DURATION.NEWS)) {
        setNews(cachedData.data);
        setLoading({ isLoading: false, error: null });
        return;
      }

      // Fetch fresh data
      const data = await newsApi.searchNews(query, sortBy, pageSize, page);
      
      // Cache the data
      const cacheData: CachedNewsData = {
        data,
        timestamp: Date.now(),
        cacheKey: searchCacheKey
      };
      setToLocalStorage(`${STORAGE_KEYS.CACHED_NEWS}_${searchCacheKey}`, cacheData);

      setNews(data);
      setLoading({ isLoading: false, error: null });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to search news';
      setLoading({ isLoading: false, error: errorMessage });
    }
  }, [pageSize, page]);

  const refreshNews = useCallback(() => {
    // Clear cache and fetch fresh data
    const cachedKeys = Object.keys(localStorage).filter(key => 
      key.startsWith(STORAGE_KEYS.CACHED_NEWS)
    );
    cachedKeys.forEach(key => localStorage.removeItem(key));
    
    fetchNews();
  }, [fetchNews]);

  useEffect(() => {
    if (autoFetch) {
      fetchNews();
    }
  }, [fetchNews, autoFetch]);

  return {
    news,
    loading: loading.isLoading,
    error: loading.error,
    fetchNews,
    searchNews,
    refreshNews,
  };
}

export function useNewsSearch() {
  const [searchResults, setSearchResults] = useState<NewsResponse | null>(null);
  const [loading, setLoading] = useState<LoadingState>({
    isLoading: false,
    error: null
  });

  const search = useCallback(async (
    query: string,
    sortBy: 'relevancy' | 'popularity' | 'publishedAt' = 'publishedAt',
    pageSize: number = 20,
    page: number = 1
  ) => {
    if (!query.trim()) {
      setSearchResults(null);
      return;
    }

    setLoading({ isLoading: true, error: null });

    try {
      const data = await newsApi.searchNews(query, sortBy, pageSize, page);
      setSearchResults(data);
      setLoading({ isLoading: false, error: null });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to search news';
      setLoading({ isLoading: false, error: errorMessage });
    }
  }, []);

  const clearSearch = useCallback(() => {
    setSearchResults(null);
    setLoading({ isLoading: false, error: null });
  }, []);

  return {
    searchResults,
    loading: loading.isLoading,
    error: loading.error,
    search,
    clearSearch,
  };
}

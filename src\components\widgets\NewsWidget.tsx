'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Skeleton } from '@/components/ui/skeleton';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Newspaper, 
  Search, 
  RefreshCw, 
  ExternalLink, 
  Heart,
  Clock,
  Filter
} from 'lucide-react';
import { useNews } from '@/hooks/useNews';
import { useApp } from '@/contexts/AppContext';
import { useUserPreferences } from '@/contexts/UserPreferencesContext';
import { formatRelativeTime, truncateText, getImageUrl } from '@/lib/utils';
import { NEWS_CATEGORIES } from '@/lib/constants';
import { NewsCategory } from '@/types';
import { useToast } from '@/hooks/useToast';

export function NewsWidget() {
  const { preferences } = useUserPreferences();
  const { addToFavorites, isFavorite, setSelectedArticle } = useApp();
  const { toast } = useToast();
  
  const [selectedCategory, setSelectedCategory] = useState<NewsCategory>('general');
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);

  const {
    news,
    loading,
    error,
    fetchNews,
    searchNews,
    refreshNews,
  } = useNews({
    category: selectedCategory,
    pageSize: 6,
    autoFetch: true,
  });

  const handleCategoryChange = (category: NewsCategory) => {
    setSelectedCategory(category);
    setSearchQuery('');
  };

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!searchQuery.trim()) {
      fetchNews();
      return;
    }

    try {
      await searchNews(searchQuery);
      toast({
        title: "Search Complete",
        description: `Found articles for "${searchQuery}"`,
      });
    } catch (error) {
      toast({
        title: "Search Error",
        description: "Failed to search articles",
        variant: "destructive",
      });
    }
  };

  const handleFavorite = (article: any) => {
    addToFavorites(article);
    toast({
      title: "Added to Favorites",
      description: "Article saved to your favorites",
    });
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Newspaper className="h-5 w-5" />
            <span>Latest News</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {Array.from({ length: 3 }).map((_, i) => (
            <div key={i} className="flex space-x-4">
              <Skeleton className="h-20 w-32 rounded" />
              <div className="flex-1 space-y-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-3 w-1/2" />
              </div>
            </div>
          ))}
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Newspaper className="h-5 w-5" />
            <span>Latest News</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-muted-foreground mb-4">{error}</p>
            <Button onClick={refreshNews} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Newspaper className="h-5 w-5" />
            <span>Latest News</span>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setShowFilters(!showFilters)}
            >
              <Filter className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={refreshNews}
              disabled={loading}
            >
              <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Search and Filters */}
        <div className="space-y-4">
          <form onSubmit={handleSearch} className="flex space-x-2">
            <Input
              placeholder="Search news..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="flex-1"
            />
            <Button type="submit" size="icon" variant="outline">
              <Search className="h-4 w-4" />
            </Button>
          </form>

          {showFilters && (
            <div className="flex space-x-2">
              <Select value={selectedCategory} onValueChange={handleCategoryChange}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent>
                  {NEWS_CATEGORIES.map((category) => (
                    <SelectItem key={category.value} value={category.value}>
                      {category.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
        </div>

        {/* Articles */}
        {news && news.articles.length > 0 ? (
          <div className="space-y-4">
            {news.articles.slice(0, 6).map((article, index) => (
              <div
                key={index}
                className="flex space-x-4 p-3 rounded-lg border hover:bg-accent/50 transition-colors cursor-pointer"
                onClick={() => setSelectedArticle(article)}
              >
                {article.urlToImage && (
                  <div className="relative h-20 w-32 flex-shrink-0">
                    <Image
                      src={getImageUrl(article.urlToImage)}
                      alt={article.title}
                      fill
                      className="object-cover rounded"
                      sizes="(max-width: 768px) 128px, 128px"
                    />
                  </div>
                )}
                <div className="flex-1 min-w-0">
                  <h4 className="font-semibold text-sm line-clamp-2 mb-1">
                    {article.title}
                  </h4>
                  <p className="text-xs text-muted-foreground line-clamp-2 mb-2">
                    {truncateText(article.description || '', 120)}
                  </p>
                  <div className="flex items-center justify-between text-xs text-muted-foreground">
                    <div className="flex items-center space-x-2">
                      <span>{article.source.name}</span>
                      <span>•</span>
                      <div className="flex items-center space-x-1">
                        <Clock className="h-3 w-3" />
                        <span>{formatRelativeTime(article.publishedAt)}</span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleFavorite(article);
                        }}
                      >
                        <Heart 
                          className={`h-3 w-3 ${
                            isFavorite(article.url) 
                              ? 'fill-red-500 text-red-500' 
                              : 'text-muted-foreground'
                          }`} 
                        />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6"
                        onClick={(e) => {
                          e.stopPropagation();
                          window.open(article.url, '_blank');
                        }}
                      >
                        <ExternalLink className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <p className="text-muted-foreground">No articles found</p>
          </div>
        )}

        {/* View More */}
        {news && news.articles.length > 6 && (
          <div className="text-center">
            <Button variant="outline" asChild>
              <a href="/news">View All News</a>
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

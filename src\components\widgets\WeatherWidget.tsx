'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  Cloud, 
  Sun, 
  CloudRain, 
  CloudSnow, 
  Wind, 
  Droplets, 
  Eye, 
  Thermometer,
  MapPin,
  Search,
  RefreshCw,
  Navigation
} from 'lucide-react';
import { useWeather, useWeatherForecast } from '@/hooks/useWeather';
import { useUserPreferences } from '@/contexts/UserPreferencesContext';
import { formatTemperature, formatDate } from '@/lib/utils';
// import { useToast } from '@/hooks/useToast';

const getWeatherIcon = (condition: string) => {
  const lower = condition.toLowerCase();
  if (lower.includes('rain')) return CloudRain;
  if (lower.includes('snow')) return CloudSnow;
  if (lower.includes('cloud')) return Cloud;
  if (lower.includes('clear') || lower.includes('sun')) return Sun;
  return Cloud;
};

export function WeatherWidget() {
  const { preferences } = useUserPreferences();
  const [searchCity, setSearchCity] = useState('');
  // const { toast } = useToast();

  const {
    weather,
    loading: weatherLoading,
    error: weatherError,
    currentCity,
    fetchWeatherByCity,
    fetchWeatherByLocation,
    refreshWeather,
  } = useWeather({
    city: preferences.defaultCity,
    autoFetch: true,
  });

  const {
    forecast,
    loading: forecastLoading,
    error: forecastError,
    fetchForecast,
  } = useWeatherForecast(currentCity);

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!searchCity.trim()) return;

    try {
      await fetchWeatherByCity(searchCity);
      await fetchForecast(searchCity);
      setSearchCity('');
      console.log(`Weather updated for ${searchCity}`);
    } catch (error) {
      console.error("Failed to fetch weather data:", error);
    }
  };

  const handleLocationWeather = async () => {
    try {
      await fetchWeatherByLocation();
      console.log("Location weather updated");
    } catch (error) {
      console.error("Unable to get location:", error);
    }
  };

  if (weatherLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Cloud className="h-5 w-5" />
            <span>Weather</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Skeleton className="h-8 w-full" />
          <Skeleton className="h-16 w-full" />
          <div className="grid grid-cols-2 gap-4">
            <Skeleton className="h-12 w-full" />
            <Skeleton className="h-12 w-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (weatherError) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Cloud className="h-5 w-5" />
            <span>Weather</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-muted-foreground mb-4">{weatherError}</p>
            <Button onClick={refreshWeather} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  const WeatherIcon = weather ? getWeatherIcon(weather.weather[0].main) : Cloud;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Cloud className="h-5 w-5" />
            <span>Weather</span>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={refreshWeather}
            disabled={weatherLoading}
          >
            <RefreshCw className={`h-4 w-4 ${weatherLoading ? 'animate-spin' : ''}`} />
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Search */}
        <form onSubmit={handleSearch} className="flex space-x-2">
          <Input
            placeholder="Search city..."
            value={searchCity}
            onChange={(e) => setSearchCity(e.target.value)}
            className="flex-1"
          />
          <Button type="submit" size="icon" variant="outline">
            <Search className="h-4 w-4" />
          </Button>
          <Button
            type="button"
            size="icon"
            variant="outline"
            onClick={handleLocationWeather}
          >
            <Navigation className="h-4 w-4" />
          </Button>
        </form>

        {weather && (
          <>
            {/* Current Weather */}
            <div className="space-y-4">
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <MapPin className="h-4 w-4" />
                <span>{weather.name}, {weather.sys.country}</span>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <WeatherIcon className="h-12 w-12 text-primary" />
                  <div>
                    <div className="text-3xl font-bold">
                      {formatTemperature(weather.main.temp, preferences.temperatureUnit)}
                    </div>
                    <div className="text-sm text-muted-foreground capitalize">
                      {weather.weather[0].description}
                    </div>
                  </div>
                </div>
                <div className="text-right text-sm">
                  <div>Feels like</div>
                  <div className="font-semibold">
                    {formatTemperature(weather.main.feels_like, preferences.temperatureUnit)}
                  </div>
                </div>
              </div>

              {/* Weather Details */}
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex items-center space-x-2">
                  <Droplets className="h-4 w-4 text-blue-500" />
                  <span>Humidity: {weather.main.humidity}%</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Wind className="h-4 w-4 text-gray-500" />
                  <span>Wind: {weather.wind.speed} m/s</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Eye className="h-4 w-4 text-purple-500" />
                  <span>Visibility: {(weather.visibility / 1000).toFixed(1)} km</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Thermometer className="h-4 w-4 text-red-500" />
                  <span>Pressure: {weather.main.pressure} hPa</span>
                </div>
              </div>
            </div>

            {/* 5-Day Forecast */}
            {forecast && !forecastLoading && (
              <div className="space-y-2">
                <h4 className="font-semibold text-sm">5-Day Forecast</h4>
                <div className="space-y-2">
                  {forecast.list
                    .filter((_, index) => index % 8 === 0) // Get one forecast per day
                    .slice(0, 5)
                    .map((item, index) => {
                      const ForecastIcon = getWeatherIcon(item.weather[0].main);
                      return (
                        <div key={index} className="flex items-center justify-between text-sm">
                          <span className="w-16">
                            {index === 0 ? 'Today' : formatDate(item.dt_txt).split(',')[0]}
                          </span>
                          <div className="flex items-center space-x-2">
                            <ForecastIcon className="h-4 w-4" />
                            <span className="capitalize text-xs">
                              {item.weather[0].description}
                            </span>
                          </div>
                          <span className="font-semibold">
                            {formatTemperature(item.main.temp, preferences.temperatureUnit)}
                          </span>
                        </div>
                      );
                    })}
                </div>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
}

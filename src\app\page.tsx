import { Layout } from '@/components/layout/Layout';
import { NewsWidget } from '@/components/widgets/NewsWidget';
import { WeatherWidget } from '@/components/widgets/WeatherWidget';
import { CountryWidget } from '@/components/widgets/CountryWidget';
import { CryptoWidget } from '@/components/widgets/CryptoWidget';

export default function HomePage() {
  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="space-y-2">
          <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
          <p className="text-muted-foreground">
            Stay updated with the latest news, weather, and market information.
          </p>
        </div>

        {/* Widgets Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {/* Weather Widget */}
          <div className="md:col-span-1">
            <WeatherWidget />
          </div>

          {/* News Widget */}
          <div className="md:col-span-1 lg:col-span-2">
            <NewsWidget />
          </div>

          {/* Crypto Widget */}
          <div className="md:col-span-1">
            <CryptoWidget />
          </div>

          {/* Country Widget */}
          <div className="md:col-span-1 lg:col-span-2">
            <CountryWidget />
          </div>
        </div>
      </div>
    </Layout>
  );
}

# News & Weather Dashboard

A comprehensive dashboard application built with Next.js 14, TypeScript, and Tailwind CSS that aggregates data from multiple APIs to provide news, weather, country information, and cryptocurrency data.

## 🚀 Features

### Core Features
- **Responsive Dashboard Layout** - Mobile-first design with adaptive grid system
- **Dark/Light Mode Toggle** - System preference detection with manual override
- **Real-time Data Updates** - Automatic refresh and caching mechanisms
- **Error Handling & Retry** - Comprehensive error boundaries with retry functionality
- **Loading States** - Skeleton loaders for better user experience

### News Section
- Latest news articles with headlines, summaries, and images
- Category filtering (Technology, Business, Sports, Entertainment, etc.)
- Search functionality for news articles
- Article modal/detail view with full content
- Favorite articles functionality
- Multiple news sources integration

### Weather Widget
- Current weather for user's location or selected city
- 5-day forecast with temperature and conditions
- Weather icons and visual indicators
- City search functionality
- Location-based weather using geolocation API
- Temperature unit conversion (Celsius/Fahrenheit)

### Country Information Panel
- Country selector with search/autocomplete
- Display country details (flag, population, capital, languages)
- Currency information and statistics
- Interactive country data visualization
- Regional and timezone information

### Cryptocurrency Tracker
- Top cryptocurrencies with current prices
- Price change indicators (up/down with colors)
- Search specific coins
- Multiple currency support
- Market cap and volume data
- Real-time price updates

## 🛠 Tech Stack

- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript (strict mode)
- **Styling**: Tailwind CSS
- **UI Components**: Shadcn/ui
- **Animations**: Aceternity UI
- **State Management**: React Context API + Custom Hooks
- **Data Fetching**: Custom hooks with caching
- **Icons**: Lucide React

## 📡 APIs Integrated

### Primary APIs
- **NewsAPI**: https://newsapi.org/ (News articles)
- **OpenWeatherMap**: https://openweathermap.org/api (Weather data)
- **REST Countries**: https://restcountries.com/ (Country information)
- **CoinGecko**: https://coingecko.com/en/api (Cryptocurrency data)

### Fallback APIs
- **JSONPlaceholder**: https://jsonplaceholder.typicode.com/ (Demo news data)
- **WeatherAPI**: https://weatherapi.com/ (Alternative weather source)
- **GNews API**: https://gnews.io/ (Alternative news source)

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ 
- npm, yarn, or pnpm

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd news-weather-dashboard
```

2. **Install dependencies**
```bash
npm install
# or
yarn install
# or
pnpm install
```

3. **Set up environment variables**
```bash
cp .env.local.example .env.local
```

Edit `.env.local` with your API keys:
```env
# News API
NEXT_PUBLIC_NEWS_API_KEY=your_news_api_key_here
NEXT_PUBLIC_GNEWS_API_KEY=your_gnews_api_key_here

# Weather API
NEXT_PUBLIC_WEATHER_API_KEY=your_openweather_api_key_here
NEXT_PUBLIC_WEATHERAPI_KEY=your_weatherapi_key_here

# Other APIs
NEXT_PUBLIC_UNSPLASH_API_KEY=your_unsplash_api_key_here
NEXT_PUBLIC_COINGECKO_API_KEY=your_coingecko_api_key_here

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

4. **Run the development server**
```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

5. **Open your browser**
Navigate to [http://localhost:3000](http://localhost:3000)

## 🔑 API Key Setup

### Required API Keys

1. **NewsAPI** (Free tier: 100 requests/day)
   - Visit: https://newsapi.org/
   - Sign up for a free account
   - Get your API key from the dashboard

2. **OpenWeatherMap** (Free tier: 1000 calls/day)
   - Visit: https://openweathermap.org/api
   - Create an account
   - Subscribe to the free plan
   - Get your API key

### Optional API Keys

3. **GNews API** (Free tier: 100 requests/day)
   - Visit: https://gnews.io/
   - Alternative news source

4. **WeatherAPI** (Free tier: 1 million calls/month)
   - Visit: https://weatherapi.com/
   - Alternative weather source

5. **Unsplash** (Free tier: 50 requests/hour)
   - Visit: https://unsplash.com/developers
   - For high-quality images

**Note**: The app includes fallback mechanisms and demo data, so it will work even without API keys, though with limited functionality.

## 📁 Project Structure

```
src/
├── app/                    # Next.js app directory
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Home page
├── components/
│   ├── ui/                # Shadcn UI components
│   ├── layout/            # Layout components
│   │   ├── Header.tsx
│   │   ├── Sidebar.tsx
│   │   └── Layout.tsx
│   └── widgets/           # Dashboard widgets
│       ├── NewsWidget.tsx
│       ├── WeatherWidget.tsx
│       ├── CountryWidget.tsx
│       └── CryptoWidget.tsx
├── contexts/              # React contexts
│   ├── ThemeContext.tsx
│   ├── UserPreferencesContext.tsx
│   └── AppContext.tsx
├── hooks/                 # Custom hooks
│   ├── useNews.ts
│   ├── useWeather.ts
│   ├── useCountries.ts
│   ├── useCrypto.ts
│   ├── useLocalStorage.ts
│   └── useToast.ts
├── lib/                   # Utilities
│   ├── api.ts            # API functions
│   ├── utils.ts          # Utility functions
│   └── constants.ts      # App constants
└── types/                 # TypeScript definitions
    └── index.ts
```

## 🎨 UI Components Used

### Shadcn/ui Components
- Card, CardHeader, CardContent, CardTitle
- Button with variants
- Input with search functionality
- Select/Dropdown for filters
- Dialog/Modal for article details
- Skeleton loaders
- Toast notifications

### Custom Components
- Responsive layout system
- Weather icons and indicators
- Country flag displays
- Cryptocurrency price charts
- Loading states and error boundaries

## 🔧 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## 🌟 Key Features Implemented

### Performance Optimizations
- Image optimization using Next.js Image component
- API response caching to reduce redundant calls
- Lazy loading for images and components
- Debounced search inputs
- Memoization of expensive calculations

### Error Handling
- Network error handling with user-friendly messages
- API rate limit handling
- Fallback UI when APIs are unavailable
- Retry mechanisms for failed requests
- Loading timeout handling

### Responsive Design
- Mobile-first approach with Tailwind CSS
- Flexible grid system that adapts to screen sizes
- Touch-friendly interface for mobile devices
- Optimized font sizes and spacing
- Collapsible navigation on smaller screens

### Accessibility
- ARIA labels for screen readers
- Keyboard navigation support
- High contrast mode compatibility
- Focus management
- Semantic HTML structure

## 🚀 Deployment

### Deploy to Vercel

1. **Push to GitHub**
```bash
git add .
git commit -m "Initial commit"
git push origin main
```

2. **Deploy to Vercel**
- Visit [vercel.com](https://vercel.com)
- Import your GitHub repository
- Add environment variables in Vercel dashboard
- Deploy

3. **Environment Variables in Vercel**
Add all the environment variables from your `.env.local` file to the Vercel project settings.

## 🐛 Known Issues & Limitations

- API rate limits may cause temporary unavailability
- Some news images may not load due to CORS policies
- Geolocation requires HTTPS in production
- Weather data updates every 10 minutes (cached)

## 🔮 Future Improvements

- PWA features (offline capability, install prompt)
- Real-time updates using WebSockets
- Data visualization using charts
- Social sharing functionality
- Advanced search filters with date ranges
- User authentication and personalization
- Export functionality (PDF reports, data export)

## 📄 License

This project is licensed under the MIT License.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📞 Support

For questions or support, please contact:
- Email: <EMAIL>
- GitHub Issues: [Create an issue](https://github.com/your-repo/issues)

---

Built with ❤️ using Next.js, TypeScript, and modern web technologies.

import { useState, useEffect, useCallback } from 'react';
import { CryptoCurrency, LoadingState } from '@/types';
import { cryptoApi } from '@/lib/api';
import { CACHE_DURATION, STORAGE_KEYS } from '@/lib/constants';
import { getFromLocalStorage, setToLocalStorage, isCacheValid, createCacheKey } from '@/lib/utils';

interface CachedCryptoData {
  data: CryptoCurrency[];
  timestamp: number;
  cacheKey: string;
}

interface UseCryptoOptions {
  currency?: string;
  perPage?: number;
  page?: number;
  autoFetch?: boolean;
}

export function useCrypto(options: UseCryptoOptions = {}) {
  const {
    currency = 'usd',
    perPage = 10,
    page = 1,
    autoFetch = true
  } = options;

  const [cryptocurrencies, setCryptocurrencies] = useState<CryptoCurrency[]>([]);
  const [loading, setLoading] = useState<LoadingState>({
    isLoading: false,
    error: null
  });

  const cacheKey = createCacheKey('crypto', currency, perPage.toString(), page.toString());

  const fetchCryptocurrencies = useCallback(async () => {
    setLoading({ isLoading: true, error: null });

    try {
      // Check cache first
      const cachedData = getFromLocalStorage<CachedCryptoData | null>(
        `${STORAGE_KEYS.CACHED_CRYPTO}_${cacheKey}`,
        null
      );

      if (cachedData && isCacheValid(cachedData.timestamp, CACHE_DURATION.CRYPTO)) {
        setCryptocurrencies(cachedData.data);
        setLoading({ isLoading: false, error: null });
        return;
      }

      // Fetch fresh data
      const data = await cryptoApi.getTopCryptocurrencies(currency, perPage, page);
      
      // Cache the data
      const cacheData: CachedCryptoData = {
        data,
        timestamp: Date.now(),
        cacheKey
      };
      setToLocalStorage(`${STORAGE_KEYS.CACHED_CRYPTO}_${cacheKey}`, cacheData);

      setCryptocurrencies(data);
      setLoading({ isLoading: false, error: null });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch cryptocurrencies';
      setLoading({ isLoading: false, error: errorMessage });
    }
  }, [currency, perPage, page, cacheKey]);

  const refreshCrypto = useCallback(() => {
    // Clear cache and fetch fresh data
    const cachedKeys = Object.keys(localStorage).filter(key => 
      key.startsWith(STORAGE_KEYS.CACHED_CRYPTO)
    );
    cachedKeys.forEach(key => localStorage.removeItem(key));
    
    fetchCryptocurrencies();
  }, [fetchCryptocurrencies]);

  useEffect(() => {
    if (autoFetch) {
      fetchCryptocurrencies();
    }
  }, [fetchCryptocurrencies, autoFetch]);

  return {
    cryptocurrencies,
    loading: loading.isLoading,
    error: loading.error,
    fetchCryptocurrencies,
    refreshCrypto,
  };
}

export function useCryptoSearch() {
  const [searchResults, setSearchResults] = useState<any>(null);
  const [loading, setLoading] = useState<LoadingState>({
    isLoading: false,
    error: null
  });

  const searchCrypto = useCallback(async (query: string) => {
    if (!query.trim()) {
      setSearchResults(null);
      return;
    }

    setLoading({ isLoading: true, error: null });

    try {
      const data = await cryptoApi.searchCryptocurrency(query);
      setSearchResults(data);
      setLoading({ isLoading: false, error: null });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to search cryptocurrencies';
      setLoading({ isLoading: false, error: errorMessage });
    }
  }, []);

  const clearSearch = useCallback(() => {
    setSearchResults(null);
    setLoading({ isLoading: false, error: null });
  }, []);

  return {
    searchResults,
    loading: loading.isLoading,
    error: loading.error,
    searchCrypto,
    clearSearch,
  };
}

# Deployment Guide

This guide will help you deploy the News & Weather Dashboard to Vercel.

## Prerequisites

1. **Node.js 18+** installed on your local machine
2. **Git** for version control
3. **Vercel account** (free tier available)
4. **API Keys** for the services you want to use

## Step 1: Prepare Your Project

1. **Install dependencies**:
```bash
npm install
```

2. **Test locally**:
```bash
npm run dev
```

3. **Build the project**:
```bash
npm run build
```

## Step 2: Set Up Environment Variables

Create a `.env.local` file with your API keys:

```env
# News API (Required for news functionality)
NEXT_PUBLIC_NEWS_API_KEY=your_news_api_key_here

# Weather API (Required for weather functionality)
NEXT_PUBLIC_WEATHER_API_KEY=your_openweather_api_key_here

# Optional APIs
NEXT_PUBLIC_GNEWS_API_KEY=your_gnews_api_key_here
NEXT_PUBLIC_WEATHERAPI_KEY=your_weatherapi_key_here
NEXT_PUBLIC_UNSPLASH_API_KEY=your_unsplash_api_key_here

# App Configuration
NEXT_PUBLIC_APP_URL=https://your-domain.vercel.app
```

## Step 3: Deploy to Vercel

### Option A: Deploy via Vercel CLI

1. **Install Vercel CLI**:
```bash
npm i -g vercel
```

2. **Login to Vercel**:
```bash
vercel login
```

3. **Deploy**:
```bash
vercel
```

4. **Add environment variables**:
```bash
vercel env add NEXT_PUBLIC_NEWS_API_KEY
vercel env add NEXT_PUBLIC_WEATHER_API_KEY
# Add all other environment variables
```

5. **Redeploy with environment variables**:
```bash
vercel --prod
```

### Option B: Deploy via Vercel Dashboard

1. **Push to GitHub**:
```bash
git add .
git commit -m "Initial deployment"
git push origin main
```

2. **Import to Vercel**:
   - Go to [vercel.com](https://vercel.com)
   - Click "New Project"
   - Import your GitHub repository
   - Configure project settings

3. **Add Environment Variables**:
   - In Vercel dashboard, go to Project Settings
   - Navigate to "Environment Variables"
   - Add all the environment variables from your `.env.local`

4. **Deploy**:
   - Vercel will automatically deploy your project
   - You'll get a live URL

## Step 4: Configure Domain (Optional)

1. **Add Custom Domain**:
   - In Vercel dashboard, go to Project Settings
   - Navigate to "Domains"
   - Add your custom domain

2. **Update Environment Variables**:
   - Update `NEXT_PUBLIC_APP_URL` to your custom domain

## Step 5: Verify Deployment

1. **Check all features**:
   - ✅ Dashboard loads correctly
   - ✅ News widget displays articles
   - ✅ Weather widget shows current weather
   - ✅ Country selector works
   - ✅ Cryptocurrency data loads
   - ✅ Theme switching works
   - ✅ Search functionality works
   - ✅ Error handling displays properly

2. **Test on different devices**:
   - ✅ Desktop browsers
   - ✅ Mobile devices
   - ✅ Tablet devices

## API Key Setup Instructions

### NewsAPI (Free: 100 requests/day)
1. Visit https://newsapi.org/
2. Sign up for a free account
3. Get your API key from the dashboard
4. Add to `NEXT_PUBLIC_NEWS_API_KEY`

### OpenWeatherMap (Free: 1000 calls/day)
1. Visit https://openweathermap.org/api
2. Create an account
3. Subscribe to the free plan
4. Get your API key
5. Add to `NEXT_PUBLIC_WEATHER_API_KEY`

### Optional APIs

#### GNews API (Free: 100 requests/day)
1. Visit https://gnews.io/
2. Sign up and get API key
3. Add to `NEXT_PUBLIC_GNEWS_API_KEY`

#### WeatherAPI (Free: 1 million calls/month)
1. Visit https://weatherapi.com/
2. Sign up and get API key
3. Add to `NEXT_PUBLIC_WEATHERAPI_KEY`

#### Unsplash (Free: 50 requests/hour)
1. Visit https://unsplash.com/developers
2. Create an application
3. Get your access key
4. Add to `NEXT_PUBLIC_UNSPLASH_API_KEY`

## Troubleshooting

### Common Issues

1. **Build Errors**:
   - Check TypeScript errors: `npm run lint`
   - Ensure all dependencies are installed
   - Verify environment variables are set

2. **API Errors**:
   - Verify API keys are correct
   - Check API rate limits
   - Ensure environment variables are properly set in Vercel

3. **Image Loading Issues**:
   - Check `next.config.js` image domains
   - Verify image URLs are accessible
   - Check CORS policies

4. **Hydration Errors**:
   - Ensure server and client render the same content
   - Check for browser-only code in components
   - Use `suppressHydrationWarning` where appropriate

### Performance Optimization

1. **Enable Analytics**:
   - Add Vercel Analytics to monitor performance
   - Set up error tracking

2. **Optimize Images**:
   - Use Next.js Image component (already implemented)
   - Configure image optimization in Vercel

3. **Monitor API Usage**:
   - Track API call frequency
   - Implement proper caching (already implemented)
   - Monitor rate limits

## Security Considerations

1. **Environment Variables**:
   - Never commit API keys to version control
   - Use Vercel's environment variable system
   - Rotate API keys regularly

2. **CORS and Security Headers**:
   - Configured in `vercel.json`
   - Additional security headers added

3. **API Rate Limiting**:
   - Implement client-side rate limiting
   - Cache responses appropriately
   - Handle rate limit errors gracefully

## Monitoring and Maintenance

1. **Set up monitoring**:
   - Vercel Analytics for performance
   - Error tracking for issues
   - API usage monitoring

2. **Regular updates**:
   - Keep dependencies updated
   - Monitor API changes
   - Update documentation

3. **Backup**:
   - Regular code backups
   - Environment variable backups
   - User data considerations

## Support

If you encounter issues during deployment:

1. Check the [Vercel documentation](https://vercel.com/docs)
2. Review the project's GitHub issues
3. Contact support through the appropriate channels

---

**Deployment Checklist**:
- [ ] Dependencies installed
- [ ] Environment variables configured
- [ ] Project builds successfully
- [ ] All API keys obtained and configured
- [ ] Deployed to Vercel
- [ ] Custom domain configured (optional)
- [ ] All features tested
- [ ] Performance optimized
- [ ] Monitoring set up

import { useState, useEffect, useCallback } from 'react';
import { Country, LoadingState } from '@/types';
import { countriesApi } from '@/lib/api';
import { CACHE_DURATION, STORAGE_KEYS } from '@/lib/constants';
import { getFromLocalStorage, setToLocalStorage, isCacheValid, createCacheKey } from '@/lib/utils';

interface CachedCountriesData {
  data: Country[];
  timestamp: number;
  cacheKey: string;
}

export function useCountries() {
  const [countries, setCountries] = useState<Country[]>([]);
  const [loading, setLoading] = useState<LoadingState>({
    isLoading: false,
    error: null
  });

  const fetchCountries = useCallback(async () => {
    setLoading({ isLoading: true, error: null });
    const cacheKey = createCacheKey('countries', 'all');

    try {
      // Check cache first
      const cachedData = getFromLocalStorage<CachedCountriesData | null>(
        `${STORAGE_KEYS.CACHED_COUNTRIES}_${cacheKey}`,
        null
      );

      if (cachedData && isCacheValid(cachedData.timestamp, CACHE_DURATION.COUNTRIES)) {
        setCountries(cachedData.data);
        setLoading({ isLoading: false, error: null });
        return;
      }

      // Fetch fresh data
      const data = await countriesApi.getAllCountries();
      
      // Sort countries by name
      const sortedData = data.sort((a, b) => 
        a.name.common.localeCompare(b.name.common)
      );
      
      // Cache the data
      const cacheData: CachedCountriesData = {
        data: sortedData,
        timestamp: Date.now(),
        cacheKey
      };
      setToLocalStorage(`${STORAGE_KEYS.CACHED_COUNTRIES}_${cacheKey}`, cacheData);

      setCountries(sortedData);
      setLoading({ isLoading: false, error: null });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch countries';
      setLoading({ isLoading: false, error: errorMessage });
    }
  }, []);

  useEffect(() => {
    fetchCountries();
  }, [fetchCountries]);

  return {
    countries,
    loading: loading.isLoading,
    error: loading.error,
    fetchCountries,
  };
}

export function useCountrySearch() {
  const [searchResults, setSearchResults] = useState<Country[]>([]);
  const [loading, setLoading] = useState<LoadingState>({
    isLoading: false,
    error: null
  });

  const searchCountries = useCallback(async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    setLoading({ isLoading: true, error: null });

    try {
      const data = await countriesApi.getCountryByName(query);
      setSearchResults(data);
      setLoading({ isLoading: false, error: null });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to search countries';
      setLoading({ isLoading: false, error: errorMessage });
    }
  }, []);

  const clearSearch = useCallback(() => {
    setSearchResults([]);
    setLoading({ isLoading: false, error: null });
  }, []);

  return {
    searchResults,
    loading: loading.isLoading,
    error: loading.error,
    searchCountries,
    clearSearch,
  };
}

export function useCountryDetails(countryCode?: string) {
  const [country, setCountry] = useState<Country | null>(null);
  const [loading, setLoading] = useState<LoadingState>({
    isLoading: false,
    error: null
  });

  const fetchCountryDetails = useCallback(async (code: string) => {
    if (!code.trim()) return;

    setLoading({ isLoading: true, error: null });
    const cacheKey = createCacheKey('country', code);

    try {
      // Check cache first
      const cachedData = getFromLocalStorage<CachedCountriesData | null>(
        `${STORAGE_KEYS.CACHED_COUNTRIES}_${cacheKey}`,
        null
      );

      if (cachedData && isCacheValid(cachedData.timestamp, CACHE_DURATION.COUNTRIES)) {
        setCountry(cachedData.data[0] || null);
        setLoading({ isLoading: false, error: null });
        return;
      }

      // Fetch fresh data
      const data = await countriesApi.getCountryByCode(code);
      
      // Cache the data
      const cacheData: CachedCountriesData = {
        data,
        timestamp: Date.now(),
        cacheKey
      };
      setToLocalStorage(`${STORAGE_KEYS.CACHED_COUNTRIES}_${cacheKey}`, cacheData);

      setCountry(data[0] || null);
      setLoading({ isLoading: false, error: null });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch country details';
      setLoading({ isLoading: false, error: errorMessage });
    }
  }, []);

  useEffect(() => {
    if (countryCode) {
      fetchCountryDetails(countryCode);
    }
  }, [countryCode, fetchCountryDetails]);

  return {
    country,
    loading: loading.isLoading,
    error: loading.error,
    fetchCountryDetails,
  };
}

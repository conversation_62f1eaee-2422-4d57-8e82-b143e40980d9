'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Skeleton } from '@/components/ui/skeleton';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Globe, 
  Search, 
  RefreshCw, 
  Users, 
  MapPin, 
  Languages,
  DollarSign,
  Calendar,
  Map
} from 'lucide-react';
import { useCountries, useCountryDetails } from '@/hooks/useCountries';
import { formatNumber } from '@/lib/utils';
import { useToast } from '@/hooks/useToast';

export function CountryWidget() {
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCountryCode, setSelectedCountryCode] = useState<string>('');

  const {
    countries,
    loading: countriesLoading,
    error: countriesError,
    fetchCountries,
  } = useCountries();

  const {
    country: selectedCountry,
    loading: countryLoading,
    error: countryError,
    fetchCountryDetails,
  } = useCountryDetails(selectedCountryCode);

  const filteredCountries = countries.filter(country =>
    country.name.common.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleCountrySelect = (countryCode: string) => {
    setSelectedCountryCode(countryCode);
    const country = countries.find(c => c.cca3 === countryCode);
    if (country) {
      toast({
        title: "Country Selected",
        description: `Showing details for ${country.name.common}`,
      });
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Search is handled by filtering
  };

  if (countriesLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Globe className="h-5 w-5" />
            <span>Country Information</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Skeleton className="h-10 w-full" />
          <Skeleton className="h-32 w-full" />
          <div className="grid grid-cols-2 gap-4">
            <Skeleton className="h-16 w-full" />
            <Skeleton className="h-16 w-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (countriesError) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Globe className="h-5 w-5" />
            <span>Country Information</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-muted-foreground mb-4">{countriesError}</p>
            <Button onClick={fetchCountries} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Globe className="h-5 w-5" />
            <span>Country Information</span>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={fetchCountries}
            disabled={countriesLoading}
          >
            <RefreshCw className={`h-4 w-4 ${countriesLoading ? 'animate-spin' : ''}`} />
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Search and Select */}
        <div className="space-y-4">
          <form onSubmit={handleSearch} className="flex space-x-2">
            <Input
              placeholder="Search countries..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="flex-1"
            />
            <Button type="submit" size="icon" variant="outline">
              <Search className="h-4 w-4" />
            </Button>
          </form>

          <Select value={selectedCountryCode} onValueChange={handleCountrySelect}>
            <SelectTrigger>
              <SelectValue placeholder="Select a country" />
            </SelectTrigger>
            <SelectContent className="max-h-60">
              {filteredCountries.slice(0, 50).map((country) => (
                <SelectItem key={country.cca3} value={country.cca3}>
                  <div className="flex items-center space-x-2">
                    <span>{country.flag}</span>
                    <span>{country.name.common}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Country Details */}
        {selectedCountry && (
          <div className="space-y-4">
            {countryLoading ? (
              <div className="space-y-4">
                <Skeleton className="h-20 w-full" />
                <div className="grid grid-cols-2 gap-4">
                  <Skeleton className="h-16 w-full" />
                  <Skeleton className="h-16 w-full" />
                </div>
              </div>
            ) : (
              <>
                {/* Flag and Basic Info */}
                <div className="flex items-start space-x-4 p-4 border rounded-lg">
                  <div className="relative h-16 w-24 flex-shrink-0">
                    <Image
                      src={selectedCountry.flags.png}
                      alt={`Flag of ${selectedCountry.name.common}`}
                      fill
                      className="object-cover rounded"
                      sizes="96px"
                    />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-xl font-bold">{selectedCountry.name.common}</h3>
                    <p className="text-sm text-muted-foreground">
                      {selectedCountry.name.official}
                    </p>
                    <div className="flex items-center space-x-4 mt-2 text-sm">
                      <div className="flex items-center space-x-1">
                        <MapPin className="h-4 w-4" />
                        <span>{selectedCountry.region}</span>
                      </div>
                      {selectedCountry.subregion && (
                        <span className="text-muted-foreground">
                          • {selectedCountry.subregion}
                        </span>
                      )}
                    </div>
                  </div>
                </div>

                {/* Statistics Grid */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="p-3 border rounded-lg">
                    <div className="flex items-center space-x-2 mb-1">
                      <Users className="h-4 w-4 text-blue-500" />
                      <span className="text-sm font-medium">Population</span>
                    </div>
                    <p className="text-lg font-bold">
                      {formatNumber(selectedCountry.population)}
                    </p>
                  </div>

                  <div className="p-3 border rounded-lg">
                    <div className="flex items-center space-x-2 mb-1">
                      <Map className="h-4 w-4 text-green-500" />
                      <span className="text-sm font-medium">Area</span>
                    </div>
                    <p className="text-lg font-bold">
                      {formatNumber(selectedCountry.area)} km²
                    </p>
                  </div>

                  {selectedCountry.capital && (
                    <div className="p-3 border rounded-lg">
                      <div className="flex items-center space-x-2 mb-1">
                        <MapPin className="h-4 w-4 text-red-500" />
                        <span className="text-sm font-medium">Capital</span>
                      </div>
                      <p className="text-lg font-bold">
                        {selectedCountry.capital[0]}
                      </p>
                    </div>
                  )}

                  {selectedCountry.currencies && (
                    <div className="p-3 border rounded-lg">
                      <div className="flex items-center space-x-2 mb-1">
                        <DollarSign className="h-4 w-4 text-yellow-500" />
                        <span className="text-sm font-medium">Currency</span>
                      </div>
                      <p className="text-lg font-bold">
                        {Object.values(selectedCountry.currencies)[0]?.name}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {Object.values(selectedCountry.currencies)[0]?.symbol}
                      </p>
                    </div>
                  )}
                </div>

                {/* Languages */}
                {selectedCountry.languages && (
                  <div className="p-3 border rounded-lg">
                    <div className="flex items-center space-x-2 mb-2">
                      <Languages className="h-4 w-4 text-purple-500" />
                      <span className="text-sm font-medium">Languages</span>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {Object.values(selectedCountry.languages).map((language, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-secondary text-secondary-foreground rounded-md text-xs"
                        >
                          {language}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* Timezones */}
                {selectedCountry.timezones && selectedCountry.timezones.length > 0 && (
                  <div className="p-3 border rounded-lg">
                    <div className="flex items-center space-x-2 mb-2">
                      <Calendar className="h-4 w-4 text-indigo-500" />
                      <span className="text-sm font-medium">Timezones</span>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {selectedCountry.timezones.slice(0, 3).map((timezone, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-secondary text-secondary-foreground rounded-md text-xs"
                        >
                          {timezone}
                        </span>
                      ))}
                      {selectedCountry.timezones.length > 3 && (
                        <span className="px-2 py-1 bg-secondary text-secondary-foreground rounded-md text-xs">
                          +{selectedCountry.timezones.length - 3} more
                        </span>
                      )}
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
        )}

        {!selectedCountry && !countryLoading && (
          <div className="text-center py-8">
            <Globe className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">Select a country to view details</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

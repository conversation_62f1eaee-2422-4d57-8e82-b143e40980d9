'use client';

import React, { createContext, useContext, useState, ReactNode } from 'react';
import { NewsArticle } from '@/types';
import { useLocalStorage } from '@/hooks/useLocalStorage';

interface AppContextType {
  // Favorites
  favoriteArticles: NewsArticle[];
  addToFavorites: (article: NewsArticle) => void;
  removeFromFavorites: (articleUrl: string) => void;
  isFavorite: (articleUrl: string) => boolean;
  
  // Recent searches
  recentSearches: string[];
  addRecentSearch: (query: string) => void;
  clearRecentSearches: () => void;
  
  // UI state
  sidebarOpen: boolean;
  setSidebarOpen: (open: boolean) => void;
  
  // Selected article for modal
  selectedArticle: NewsArticle | null;
  setSelectedArticle: (article: NewsArticle | null) => void;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

export function AppProvider({ children }: { children: ReactNode }) {
  const [favoriteArticles, setFavoriteArticles] = useLocalStorage<NewsArticle[]>('favorite_articles', []);
  const [recentSearches, setRecentSearches] = useLocalStorage<string[]>('recent_searches', []);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [selectedArticle, setSelectedArticle] = useState<NewsArticle | null>(null);

  const addToFavorites = (article: NewsArticle) => {
    setFavoriteArticles(prev => {
      const exists = prev.some(fav => fav.url === article.url);
      if (exists) return prev;
      return [article, ...prev];
    });
  };

  const removeFromFavorites = (articleUrl: string) => {
    setFavoriteArticles(prev => prev.filter(article => article.url !== articleUrl));
  };

  const isFavorite = (articleUrl: string) => {
    return favoriteArticles.some(article => article.url === articleUrl);
  };

  const addRecentSearch = (query: string) => {
    if (!query.trim()) return;
    
    setRecentSearches(prev => {
      const filtered = prev.filter(search => search !== query);
      return [query, ...filtered].slice(0, 10); // Keep only last 10 searches
    });
  };

  const clearRecentSearches = () => {
    setRecentSearches([]);
  };

  const value: AppContextType = {
    favoriteArticles,
    addToFavorites,
    removeFromFavorites,
    isFavorite,
    recentSearches,
    addRecentSearch,
    clearRecentSearches,
    sidebarOpen,
    setSidebarOpen,
    selectedArticle,
    setSelectedArticle,
  };

  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  );
}

export function useApp() {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
}

import { 
  NewsResponse, 
  GNewsResponse, 
  CurrentWeather, 
  WeatherForecast, 
  Country, 
  CryptoCurrency,
  ApiError,
  NewsCategory 
} from '@/types';
import { API_ENDPOINTS, API_KEYS, ERROR_MESSAGES } from './constants';

// Generic API fetch function with error handling
async function apiRequest<T>(url: string, options?: RequestInit): Promise<T> {
  try {
    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...options?.headers,
      },
    });

    if (!response.ok) {
      if (response.status === 429) {
        throw new Error(ERROR_MESSAGES.RATE_LIMIT_EXCEEDED);
      }
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    if (error instanceof Error) {
      throw error;
    }
    throw new Error(ERROR_MESSAGES.NETWORK_ERROR);
  }
}

// News API functions
export const newsApi = {
  async getTopHeadlines(
    category?: NewsCategory,
    country: string = 'us',
    pageSize: number = 20,
    page: number = 1
  ): Promise<NewsResponse> {
    if (!API_KEYS.NEWS_API) {
      // Fallback to JSONPlaceholder for demo
      const posts = await apiRequest<any[]>(`${API_ENDPOINTS.JSONPLACEHOLDER}/posts`);
      return {
        status: 'ok',
        totalResults: posts.length,
        articles: posts.slice(0, pageSize).map((post, index) => ({
          title: post.title,
          description: post.body.substring(0, 100) + '...',
          content: post.body,
          url: `https://example.com/article/${post.id}`,
          urlToImage: `https://picsum.photos/400/300?random=${index}`,
          publishedAt: new Date().toISOString(),
          source: {
            id: 'jsonplaceholder',
            name: 'Demo News'
          },
          author: 'Demo Author'
        }))
      };
    }

    const params = new URLSearchParams({
      apiKey: API_KEYS.NEWS_API,
      country,
      pageSize: pageSize.toString(),
      page: page.toString(),
    });

    if (category && category !== 'general') {
      params.append('category', category);
    }

    return apiRequest<NewsResponse>(
      `${API_ENDPOINTS.NEWS_API}/top-headlines?${params}`
    );
  },

  async searchNews(
    query: string,
    sortBy: 'relevancy' | 'popularity' | 'publishedAt' = 'publishedAt',
    pageSize: number = 20,
    page: number = 1
  ): Promise<NewsResponse> {
    if (!API_KEYS.NEWS_API) {
      // Fallback to filtered JSONPlaceholder data
      const posts = await apiRequest<any[]>(`${API_ENDPOINTS.JSONPLACEHOLDER}/posts`);
      const filtered = posts.filter(post => 
        post.title.toLowerCase().includes(query.toLowerCase()) ||
        post.body.toLowerCase().includes(query.toLowerCase())
      );

      return {
        status: 'ok',
        totalResults: filtered.length,
        articles: filtered.slice(0, pageSize).map((post, index) => ({
          title: post.title,
          description: post.body.substring(0, 100) + '...',
          content: post.body,
          url: `https://example.com/article/${post.id}`,
          urlToImage: `https://picsum.photos/400/300?random=${index}`,
          publishedAt: new Date().toISOString(),
          source: {
            id: 'jsonplaceholder',
            name: 'Demo News'
          },
          author: 'Demo Author'
        }))
      };
    }

    const params = new URLSearchParams({
      apiKey: API_KEYS.NEWS_API,
      q: query,
      sortBy,
      pageSize: pageSize.toString(),
      page: page.toString(),
    });

    return apiRequest<NewsResponse>(
      `${API_ENDPOINTS.NEWS_API}/everything?${params}`
    );
  },
};

// Weather API functions
export const weatherApi = {
  async getCurrentWeather(city: string): Promise<CurrentWeather> {
    if (!API_KEYS.WEATHER_API) {
      throw new Error(ERROR_MESSAGES.API_KEY_MISSING);
    }

    const params = new URLSearchParams({
      q: city,
      appid: API_KEYS.WEATHER_API,
      units: 'metric',
    });

    return apiRequest<CurrentWeather>(
      `${API_ENDPOINTS.WEATHER_API}/weather?${params}`
    );
  },

  async getWeatherByCoords(lat: number, lon: number): Promise<CurrentWeather> {
    if (!API_KEYS.WEATHER_API) {
      throw new Error(ERROR_MESSAGES.API_KEY_MISSING);
    }

    const params = new URLSearchParams({
      lat: lat.toString(),
      lon: lon.toString(),
      appid: API_KEYS.WEATHER_API,
      units: 'metric',
    });

    return apiRequest<CurrentWeather>(
      `${API_ENDPOINTS.WEATHER_API}/weather?${params}`
    );
  },

  async getForecast(city: string): Promise<WeatherForecast> {
    if (!API_KEYS.WEATHER_API) {
      throw new Error(ERROR_MESSAGES.API_KEY_MISSING);
    }

    const params = new URLSearchParams({
      q: city,
      appid: API_KEYS.WEATHER_API,
      units: 'metric',
    });

    return apiRequest<WeatherForecast>(
      `${API_ENDPOINTS.WEATHER_API}/forecast?${params}`
    );
  },
};

// Countries API functions
export const countriesApi = {
  async getAllCountries(): Promise<Country[]> {
    return apiRequest<Country[]>(`${API_ENDPOINTS.COUNTRIES_API}/all`);
  },

  async getCountryByName(name: string): Promise<Country[]> {
    return apiRequest<Country[]>(`${API_ENDPOINTS.COUNTRIES_API}/name/${name}`);
  },

  async getCountryByCode(code: string): Promise<Country[]> {
    return apiRequest<Country[]>(`${API_ENDPOINTS.COUNTRIES_API}/alpha/${code}`);
  },
};

// Cryptocurrency API functions
export const cryptoApi = {
  async getTopCryptocurrencies(
    vs_currency: string = 'usd',
    per_page: number = 10,
    page: number = 1
  ): Promise<CryptoCurrency[]> {
    const params = new URLSearchParams({
      vs_currency,
      order: 'market_cap_desc',
      per_page: per_page.toString(),
      page: page.toString(),
      sparkline: 'false',
    });

    return apiRequest<CryptoCurrency[]>(
      `${API_ENDPOINTS.COINGECKO_API}/coins/markets?${params}`
    );
  },

  async searchCryptocurrency(query: string): Promise<any> {
    const params = new URLSearchParams({
      query,
    });

    return apiRequest<any>(
      `${API_ENDPOINTS.COINGECKO_API}/search?${params}`
    );
  },
};

// Geolocation helper
export const getGeolocation = (): Promise<GeolocationPosition> => {
  return new Promise((resolve, reject) => {
    if (!navigator.geolocation) {
      reject(new Error(ERROR_MESSAGES.GEOLOCATION_UNAVAILABLE));
      return;
    }

    navigator.geolocation.getCurrentPosition(
      resolve,
      (error) => {
        switch (error.code) {
          case error.PERMISSION_DENIED:
            reject(new Error(ERROR_MESSAGES.GEOLOCATION_DENIED));
            break;
          case error.POSITION_UNAVAILABLE:
            reject(new Error(ERROR_MESSAGES.GEOLOCATION_UNAVAILABLE));
            break;
          default:
            reject(new Error(ERROR_MESSAGES.GENERIC_ERROR));
            break;
        }
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 600000, // 10 minutes
      }
    );
  });
};

import { 
  NewsResponse, 
  GNewsResponse, 
  CurrentWeather, 
  WeatherForecast, 
  Country, 
  CryptoCurrency,
  ApiError,
  NewsCategory 
} from '@/types';
import { API_ENDPOINTS, API_KEYS, ERROR_MESSAGES } from './constants';

// Generic API fetch function with error handling
async function apiRequest<T>(url: string, options?: RequestInit): Promise<T> {
  try {
    const response = await fetch(url, {
      ...options,
      mode: 'cors',
      headers: {
        ...options?.headers,
      },
    });

    if (!response.ok) {
      if (response.status === 429) {
        throw new Error(ERROR_MESSAGES.RATE_LIMIT_EXCEEDED);
      }
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    if (error instanceof Error) {
      throw error;
    }
    throw new Error(ERROR_MESSAGES.NETWORK_ERROR);
  }
}

// News API functions
export const newsApi = {
  async getTopHeadlines(
    category?: NewsCategory,
    country: string = 'us',
    pageSize: number = 20,
    page: number = 1
  ): Promise<NewsResponse> {
    // Use JSONPlaceholder for demo due to CORS restrictions in development
    try {
      const posts = await apiRequest<any[]>(`${API_ENDPOINTS.JSONPLACEHOLDER}/posts`);

      // Create more realistic news data
      const newsCategories = ['Technology', 'Business', 'Sports', 'Entertainment', 'Health', 'Science'];
      const sources = ['TechCrunch', 'BBC News', 'CNN', 'Reuters', 'The Guardian', 'Associated Press'];

      return {
        status: 'ok',
        totalResults: posts.length,
        articles: posts.slice(0, pageSize).map((post, index) => {
          const randomCategory = newsCategories[index % newsCategories.length];
          const randomSource = sources[index % sources.length];
          const hoursAgo = Math.floor(Math.random() * 24) + 1;
          const publishedAt = new Date(Date.now() - hoursAgo * 60 * 60 * 1000).toISOString();

          return {
            title: post.title.charAt(0).toUpperCase() + post.title.slice(1),
            description: post.body.substring(0, 150) + '...',
            content: post.body,
            url: `https://example.com/article/${post.id}`,
            urlToImage: `https://picsum.photos/400/300?random=${index + 100}`,
            publishedAt,
            source: {
              id: randomSource.toLowerCase().replace(/\s+/g, '-'),
              name: randomSource
            },
            author: `Reporter ${index + 1}`
          };
        })
      };
    } catch (error) {
      // Fallback with static data
      return {
        status: 'ok',
        totalResults: 3,
        articles: [
          {
            title: "Breaking: Major Technology Breakthrough Announced",
            description: "Scientists have made a significant discovery that could revolutionize the tech industry...",
            content: "Full article content here...",
            url: "https://example.com/tech-breakthrough",
            urlToImage: "https://picsum.photos/400/300?random=1",
            publishedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
            source: { id: "tech-news", name: "Tech News" },
            author: "Tech Reporter"
          },
          {
            title: "Global Markets Show Positive Trends",
            description: "Financial markets around the world are showing encouraging signs of growth...",
            content: "Full article content here...",
            url: "https://example.com/market-trends",
            urlToImage: "https://picsum.photos/400/300?random=2",
            publishedAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
            source: { id: "business-today", name: "Business Today" },
            author: "Financial Analyst"
          },
          {
            title: "Climate Change Summit Reaches Key Agreements",
            description: "World leaders have come together to address pressing environmental concerns...",
            content: "Full article content here...",
            url: "https://example.com/climate-summit",
            urlToImage: "https://picsum.photos/400/300?random=3",
            publishedAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
            source: { id: "global-news", name: "Global News" },
            author: "Environmental Reporter"
          }
        ]
      };
    }
  },

  async searchNews(
    query: string,
    sortBy: 'relevancy' | 'popularity' | 'publishedAt' = 'publishedAt',
    pageSize: number = 20,
    page: number = 1
  ): Promise<NewsResponse> {
    try {
      // Use JSONPlaceholder for demo search
      const posts = await apiRequest<any[]>(`${API_ENDPOINTS.JSONPLACEHOLDER}/posts`);
      const filtered = posts.filter(post =>
        post.title.toLowerCase().includes(query.toLowerCase()) ||
        post.body.toLowerCase().includes(query.toLowerCase())
      );

      const sources = ['Search News', 'Tech Daily', 'News Wire', 'Global Times'];

      return {
        status: 'ok',
        totalResults: filtered.length,
        articles: filtered.slice(0, pageSize).map((post, index) => {
          const randomSource = sources[index % sources.length];
          const hoursAgo = Math.floor(Math.random() * 12) + 1;
          const publishedAt = new Date(Date.now() - hoursAgo * 60 * 60 * 1000).toISOString();

          return {
            title: post.title.charAt(0).toUpperCase() + post.title.slice(1),
            description: post.body.substring(0, 120) + '...',
            content: post.body,
            url: `https://example.com/search/${post.id}`,
            urlToImage: `https://picsum.photos/400/300?random=${index + 200}`,
            publishedAt,
            source: {
              id: randomSource.toLowerCase().replace(/\s+/g, '-'),
              name: randomSource
            },
            author: `Search Reporter ${index + 1}`
          };
        })
      };
    } catch (error) {
      // Return empty results if search fails
      return {
        status: 'ok',
        totalResults: 0,
        articles: []
      };
    }
  },
};

// Weather API functions
export const weatherApi = {
  async getCurrentWeather(city: string): Promise<CurrentWeather> {
    if (!API_KEYS.WEATHER_API) {
      throw new Error(ERROR_MESSAGES.API_KEY_MISSING);
    }

    const params = new URLSearchParams({
      q: city,
      appid: API_KEYS.WEATHER_API,
      units: 'metric',
    });

    return apiRequest<CurrentWeather>(
      `${API_ENDPOINTS.WEATHER_API}/weather?${params}`
    );
  },

  async getWeatherByCoords(lat: number, lon: number): Promise<CurrentWeather> {
    if (!API_KEYS.WEATHER_API) {
      throw new Error(ERROR_MESSAGES.API_KEY_MISSING);
    }

    const params = new URLSearchParams({
      lat: lat.toString(),
      lon: lon.toString(),
      appid: API_KEYS.WEATHER_API,
      units: 'metric',
    });

    return apiRequest<CurrentWeather>(
      `${API_ENDPOINTS.WEATHER_API}/weather?${params}`
    );
  },

  async getForecast(city: string): Promise<WeatherForecast> {
    if (!API_KEYS.WEATHER_API) {
      throw new Error(ERROR_MESSAGES.API_KEY_MISSING);
    }

    const params = new URLSearchParams({
      q: city,
      appid: API_KEYS.WEATHER_API,
      units: 'metric',
    });

    return apiRequest<WeatherForecast>(
      `${API_ENDPOINTS.WEATHER_API}/forecast?${params}`
    );
  },
};

// Countries API functions
export const countriesApi = {
  async getAllCountries(): Promise<Country[]> {
    try {
      return await apiRequest<Country[]>(`${API_ENDPOINTS.COUNTRIES_API}/all?fields=name,cca2,cca3,flag,flags,population,area,capital,region,subregion,languages,currencies,timezones`);
    } catch (error) {
      // Fallback with sample countries data
      return [
        {
          name: { common: "United States", official: "United States of America" },
          cca2: "US", cca3: "USA", flag: "🇺🇸",
          flags: { png: "https://flagcdn.com/w320/us.png", svg: "https://flagcdn.com/us.svg" },
          population: 331002651, area: 9833517,
          capital: ["Washington, D.C."], region: "Americas", subregion: "Northern America",
          languages: { eng: "English" },
          currencies: { USD: { name: "United States dollar", symbol: "$" } },
          timezones: ["UTC-12:00", "UTC-11:00", "UTC-10:00", "UTC-09:00", "UTC-08:00", "UTC-07:00", "UTC-06:00", "UTC-05:00", "UTC-04:00", "UTC+10:00", "UTC+12:00"],
          altSpellings: ["US"], landlocked: false, unMember: true, status: "officially-assigned",
          latlng: [38.0, -97.0], continents: ["North America"], startOfWeek: "sunday",
          capitalInfo: { latlng: [38.89, -77.05] }, car: { signs: ["USA"], side: "right" },
          coatOfArms: {}, maps: { googleMaps: "", openStreetMaps: "" }, translations: {}
        },
        {
          name: { common: "United Kingdom", official: "United Kingdom of Great Britain and Northern Ireland" },
          cca2: "GB", cca3: "GBR", flag: "🇬🇧",
          flags: { png: "https://flagcdn.com/w320/gb.png", svg: "https://flagcdn.com/gb.svg" },
          population: 67886011, area: 242495,
          capital: ["London"], region: "Europe", subregion: "Northern Europe",
          languages: { eng: "English" },
          currencies: { GBP: { name: "British pound", symbol: "£" } },
          timezones: ["UTC-08:00", "UTC-05:00", "UTC-04:00", "UTC-03:00", "UTC-02:00", "UTC+00:00", "UTC+01:00", "UTC+06:00"],
          altSpellings: ["GB"], landlocked: false, unMember: true, status: "officially-assigned",
          latlng: [54.0, -2.0], continents: ["Europe"], startOfWeek: "monday",
          capitalInfo: { latlng: [51.5, -0.08] }, car: { signs: ["GB"], side: "left" },
          coatOfArms: {}, maps: { googleMaps: "", openStreetMaps: "" }, translations: {}
        },
        {
          name: { common: "France", official: "French Republic" },
          cca2: "FR", cca3: "FRA", flag: "🇫🇷",
          flags: { png: "https://flagcdn.com/w320/fr.png", svg: "https://flagcdn.com/fr.svg" },
          population: 67391582, area: 551695,
          capital: ["Paris"], region: "Europe", subregion: "Western Europe",
          languages: { fra: "French" },
          currencies: { EUR: { name: "Euro", symbol: "€" } },
          timezones: ["UTC-10:00", "UTC-09:30", "UTC-09:00", "UTC-08:00", "UTC-04:00", "UTC-03:00", "UTC+01:00", "UTC+03:00", "UTC+04:00", "UTC+05:00", "UTC+11:00", "UTC+12:00"],
          altSpellings: ["FR"], landlocked: false, unMember: true, status: "officially-assigned",
          latlng: [46.0, 2.0], continents: ["Europe"], startOfWeek: "monday",
          capitalInfo: { latlng: [48.87, 2.33] }, car: { signs: ["F"], side: "right" },
          coatOfArms: {}, maps: { googleMaps: "", openStreetMaps: "" }, translations: {}
        }
      ];
    }
  },

  async getCountryByName(name: string): Promise<Country[]> {
    try {
      return await apiRequest<Country[]>(`${API_ENDPOINTS.COUNTRIES_API}/name/${name}?fields=name,cca2,cca3,flag,flags,population,area,capital,region,subregion,languages,currencies,timezones`);
    } catch (error) {
      return [];
    }
  },

  async getCountryByCode(code: string): Promise<Country[]> {
    try {
      return await apiRequest<Country[]>(`${API_ENDPOINTS.COUNTRIES_API}/alpha/${code}?fields=name,cca2,cca3,flag,flags,population,area,capital,region,subregion,languages,currencies,timezones`);
    } catch (error) {
      return [];
    }
  },
};

// Cryptocurrency API functions
export const cryptoApi = {
  async getTopCryptocurrencies(
    vs_currency: string = 'usd',
    per_page: number = 10,
    page: number = 1
  ): Promise<CryptoCurrency[]> {
    const params = new URLSearchParams({
      vs_currency,
      order: 'market_cap_desc',
      per_page: per_page.toString(),
      page: page.toString(),
      sparkline: 'false',
    });

    return apiRequest<CryptoCurrency[]>(
      `${API_ENDPOINTS.COINGECKO_API}/coins/markets?${params}`
    );
  },

  async searchCryptocurrency(query: string): Promise<any> {
    const params = new URLSearchParams({
      query,
    });

    return apiRequest<any>(
      `${API_ENDPOINTS.COINGECKO_API}/search?${params}`
    );
  },
};

// Geolocation helper
export const getGeolocation = (): Promise<GeolocationPosition> => {
  return new Promise((resolve, reject) => {
    if (!navigator.geolocation) {
      reject(new Error(ERROR_MESSAGES.GEOLOCATION_UNAVAILABLE));
      return;
    }

    navigator.geolocation.getCurrentPosition(
      resolve,
      (error) => {
        switch (error.code) {
          case error.PERMISSION_DENIED:
            reject(new Error(ERROR_MESSAGES.GEOLOCATION_DENIED));
            break;
          case error.POSITION_UNAVAILABLE:
            reject(new Error(ERROR_MESSAGES.GEOLOCATION_UNAVAILABLE));
            break;
          default:
            reject(new Error(ERROR_MESSAGES.GENERIC_ERROR));
            break;
        }
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 600000, // 10 minutes
      }
    );
  });
};

import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/contexts/ThemeContext";
import { UserPreferencesProvider } from "@/contexts/UserPreferencesContext";
import { AppProvider } from "@/contexts/AppContext";
// import { Toaster } from "@/components/ui/toaster";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "News & Weather Dashboard",
  description: "A comprehensive dashboard for news, weather, countries, and cryptocurrency data",
  keywords: ["news", "weather", "dashboard", "cryptocurrency", "countries"],
  authors: [{ name: "Dashboard App" }],
};

export const viewport = {
  width: "device-width",
  initialScale: 1,
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <ThemeProvider>
          <UserPreferencesProvider>
            <AppProvider>
              {children}
              {/* <Toaster /> */}
            </AppProvider>
          </UserPreferencesProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}

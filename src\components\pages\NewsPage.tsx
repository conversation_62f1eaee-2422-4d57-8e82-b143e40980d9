'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  Newspaper, 
  Search, 
  Filter, 
  Heart, 
  ExternalLink, 
  Clock,
  RefreshCw
} from 'lucide-react';
import { useNews } from '@/hooks/useNews';
import { useApp } from '@/contexts/AppContext';
import { formatRelativeTime, getImageUrl } from '@/lib/utils';
import { NEWS_CATEGORIES } from '@/lib/constants';
import { NewsCategory } from '@/types';
import { ErrorBoundary } from '@/components/common/ErrorBoundary';
import { EmptyState } from '@/components/common/EmptyState';
import { useToast } from '@/hooks/useToast';

export function NewsPage() {
  const { addToFavorites, isFavorite, setSelectedArticle } = useApp();
  const { toast } = useToast();
  
  const [selectedCategory, setSelectedCategory] = useState<NewsCategory>('general');
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);

  const {
    news,
    loading,
    error,
    searchNews,
    refreshNews,
  } = useNews({
    category: selectedCategory,
    pageSize: 20,
    autoFetch: true,
  });

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!searchQuery.trim()) return;

    try {
      await searchNews(searchQuery);
      toast({
        title: "Search Complete",
        description: `Found articles for "${searchQuery}"`,
      });
    } catch (error) {
      toast({
        title: "Search Error",
        description: "Failed to search articles",
        variant: "destructive",
      });
    }
  };

  const handleFavorite = (article: any) => {
    addToFavorites(article);
    toast({
      title: "Added to Favorites",
      description: "Article saved to your favorites",
    });
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="space-y-2">
          <h1 className="text-3xl font-bold tracking-tight">News</h1>
          <p className="text-muted-foreground">
            Stay updated with the latest news from around the world.
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {Array.from({ length: 9 }).map((_, i) => (
            <Card key={i}>
              <Skeleton className="h-48 w-full rounded-t-lg" />
              <CardContent className="p-4 space-y-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-3 w-1/2" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="space-y-2">
          <h1 className="text-3xl font-bold tracking-tight">News</h1>
          <p className="text-muted-foreground">
            Stay updated with the latest news from around the world.
          </p>
        </div>

        <EmptyState
          icon={Newspaper}
          title="Failed to load news"
          description={error}
          action={{
            label: "Try Again",
            onClick: refreshNews,
          }}
        />
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <h1 className="text-3xl font-bold tracking-tight">News</h1>
            <p className="text-muted-foreground">
              Stay updated with the latest news from around the world.
            </p>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={refreshNews}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          </Button>
        </div>

        {/* Search and Filters */}
        <Card>
          <CardContent className="p-4 space-y-4">
            <div className="flex items-center space-x-4">
              <form onSubmit={handleSearch} className="flex-1 flex space-x-2">
                <Input
                  placeholder="Search news articles..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="flex-1"
                />
                <Button type="submit" size="icon">
                  <Search className="h-4 w-4" />
                </Button>
              </form>
              <Button
                variant="outline"
                size="icon"
                onClick={() => setShowFilters(!showFilters)}
              >
                <Filter className="h-4 w-4" />
              </Button>
            </div>

            {showFilters && (
              <div className="flex space-x-4">
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {NEWS_CATEGORIES.map((category) => (
                      <SelectItem key={category.value} value={category.value}>
                        {category.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Articles Grid */}
        {news && news.articles.length > 0 ? (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {news.articles.map((article, index) => (
              <Card key={index} className="overflow-hidden hover:shadow-lg transition-shadow">
                {article.urlToImage && (
                  <div className="relative h-48 w-full">
                    <Image
                      src={getImageUrl(article.urlToImage)}
                      alt={article.title}
                      fill
                      className="object-cover"
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    />
                  </div>
                )}
                <CardContent className="p-4">
                  <div className="space-y-3">
                    <h3 
                      className="font-semibold line-clamp-2 cursor-pointer hover:text-primary"
                      onClick={() => setSelectedArticle(article)}
                    >
                      {article.title}
                    </h3>
                    <p className="text-sm text-muted-foreground line-clamp-3">
                      {article.description}
                    </p>
                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                      <div className="flex items-center space-x-2">
                        <span>{article.source.name}</span>
                        <span>•</span>
                        <div className="flex items-center space-x-1">
                          <Clock className="h-3 w-3" />
                          <span>{formatRelativeTime(article.publishedAt)}</span>
                        </div>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6"
                          onClick={() => handleFavorite(article)}
                        >
                          <Heart 
                            className={`h-3 w-3 ${
                              isFavorite(article.url) 
                                ? 'fill-red-500 text-red-500' 
                                : 'text-muted-foreground'
                            }`} 
                          />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6"
                          onClick={() => window.open(article.url, '_blank')}
                        >
                          <ExternalLink className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <EmptyState
            icon={Newspaper}
            title="No articles found"
            description="Try adjusting your search terms or category filter."
            action={{
              label: "Refresh",
              onClick: refreshNews,
            }}
          />
        )}
      </div>
    </ErrorBoundary>
  );
}
